<div class="expression-modal">
    <nz-tabset nzType="card" [(nzSelectedIndex)]="currentTab">
        <nz-tab *ngFor="let tab of tabs" [nzTitle]="tab">
            <div class="form-group">
                <div nz-row [nzGutter]="16">
                    <!-- <label class="name">资源类别</label> -->
                    <div nz-col [nzSpan]="12">
                        <nz-select style="width: 100%;" [(ngModel)]="selectedValue" (ngModelChange)="onSelectChange($event)" nzPlaceHolder="请选择指标对象类型"  [nzShowSearch]="true"
                        [nzAllowClear]="true">
                            <nz-option *ngFor="let item of selectOptions" [nzValue]="item.value" [nzLabel]="item.label">
                            </nz-option>
                        </nz-select>
                    </div>

                    <div nz-col [nzSpan]="12">
                        <nz-input-group [nzSuffix]="suffixIconSearch">
                            <input
                              type="text"
                              class="search"
                              nz-input
                              placeholder="请输入检索关键字"
                              [(ngModel)]="indexSearchKey"
                              (ngModelChange)="onInputChange($event)"
                            />
                          </nz-input-group>
                          
                        <ng-template #suffixIconSearch>
                            <span nz-icon nzType="search"></span>
                        </ng-template>
                    </div>
                </div>
                <div nz-row [nzGutter] = "16">
                    <d-data-table #myTable [dataSource]="showSource" [scrollable]="true" [tableWidthConfig]="tableColumnConfig"
                        [onlyOneColumnSort]="true" [tableHeight]="'240px'" [containFixHeaderHeight]="true" [fixHeader]="true"
                        (tableScrollEvent)="tableScrollEvent($event)" style="min-height: 240px;">
                        <thead dTableHead>
                            <tr dTableRow>
                                <th *ngFor="let colConfig of tableColumnConfig" dHeadCell [resizeEnabled]="true"
                                     [minWidth]="colConfig.minWidth!">
                                    {{ colConfig.title }}
                                </th>
                            </tr>
                        </thead>
                        <tbody dTableBody>
                            <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                                <tr dTableRow (dblclick)="selectIndex(rowItem)">
                                    <td *ngFor="let colConfig of tableColumnConfig" dTableCell>
                                        {{ rowItem[colConfig.field] }}
                                    </td>
                                </tr>
                            </ng-template>
                            
                        </tbody>
                    </d-data-table>
                </div>

            </div>

            <div class="form-group expression">
                <label class="name">{{tabs[currentTab]}}表达式</label>
                <textarea rows="3" nz-input [(ngModel)]="expression" [maxlength]="isCrossSite? 1024:1024"
                    (ngModelChange)="complexIndexCheckExpression($event)" (click)="getMousePosition($event)"
                    [ngClass]="{'correct': expression && correct === true, 'error': expression && correct === false}"
                    [disabled]="input.disabled"></textarea>
                <iconfont *ngIf="expression && !input.disabled" [icon]="'icon-delete2'" title="删除"
                    (click)="deleteExpression()"></iconfont>
            </div>

            <div class="form-group">
                <label class="name">{{tabs[currentTab]}}描述</label>
                <textarea rows="3" nz-input [(ngModel)]="description" readonly
                    [ngClass]="{'correct': expression && correct === true, 'error': expression && correct === false}"
                    [disabled]="input.disabled"></textarea>
            </div>

            <div class="form-group">
                <label class="col-sm-1 form-control-label">运算符</label>
                <div class="btn-group">
                    <ng-container *ngFor="let math of formulaMathArray">
                        <button nz-button [nzSize]="'small'" class="btn btn-primary" nz-tooltip
                            [nzTooltipTitle]="math.description" (click)="operate(math.name)"
                            [disabled]="input.disabled">
                            {{ math['name'] }}
                        </button>
                    </ng-container>
                </div>
            </div>
        </nz-tab>
    </nz-tabset>
    <!-- <div class="btn">
        <button nz-button nzType="primary" (click)="submit()">确定</button>
    </div> -->
</div>