import { Injectable } from '@angular/core';
import { Guid } from '@core/util/guid';
import * as CryptoJS from 'crypto-js'


@Injectable({
    providedIn: 'root'
})
export class SessionService {

    public readonly enabledEncode: boolean = false;

    public readonly confuseCode = {
        version: 1.0,
        date: '2020-12-12 00:00:00',
        password: '~!@#$%^&*(*))/**/'
    }

    /**
     * set the key content in the session
     * @param key
     * @param value
     */
    public async set<T>(key: string, value: T): Promise<void> {
        const _key = this.generateKey(key);
        if (value == null) {
            this.remove(_key);
            return;
        }
        let value2 = JSON.stringify(value)
        if (this.enabledEncode) {
            value2 = CryptoJS.AES.encrypt(value2, this.generateIV(key)).toString();
        }
        sessionStorage.setItem(_key, value2);
    }

    /**
     * set the key content to the session
     * @param key
     * @returns
     */
    public get<T>(key: string): T | null {
        const _key = this.generateKey(key);
        let value = sessionStorage.getItem(_key);
        if (value == null) return null;
        if (this.enabledEncode) {
            const data = CryptoJS.AES.decrypt(value, this.generateIV(key));
            value = data.toString(CryptoJS.enc.Utf8);
        }
        return JSON.parse(value);
    }

    /**
     * remove the key from session
     * @param key
     */
    public remove(key: string): void {
        const _key = this.generateKey(key);
        sessionStorage.removeItem(_key);
    }


    public generateKey(key: string): string {
        if (this.enabledEncode) {
            return CryptoJS.MD5(key + JSON.stringify(this.confuseCode)).toString();
        } else {
            return key;
        }
    }


    public generateIV(key: string): string {
        return CryptoJS.MD5(JSON.stringify(this.confuseCode) + key + '$').toString();
    }
}
