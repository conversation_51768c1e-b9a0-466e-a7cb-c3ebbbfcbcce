/* eslint-disable @typescript-eslint/explicit-member-accessibility */
import { Component, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { GenericComponent } from '@core/components/basic/generic.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { GlobalState } from '@services/global.state';
import { DeviceManagementService } from '../device-management.service';
import { DataDictionaryService } from '@services/data-dictionary.service';
import { BaseClassService } from '@services/baseclass-api-service';
import { ExpressionConfigurationComponent } from '@components/selector/expression-configuration/expression-configuration.component';
import { DateUtil } from '@core/util/date.util';
@Component({
    selector: 'app-device-info',
    templateUrl: './device-info.component.html',
    styleUrls: ['./device-info.component.less']
})
export class DeviceInfoComponent extends GenericComponent {
    eqNameRegex = /^[^<>\'\\]+$/;
    baseTypeList = [];
    equipmentId: any;
    deviceInfo =
        {
            "stationId": 0,
            "equipmentId": null,
            "equipmentName": '',
            "parentEquipmentId": '',
            "equipmentTemplateId": null,
            "equipmentTemplateName": '',
            "workStationId": null,
            "workStationName": '',
            "stationName": '',
            "monitorUnitId": null,
            "monitorUnitName": '',
            "equipmentCategory": null,
            "equipmentType": null,
            "equipmentClass": null,
            "equipmentState": null,
            "vendor": '',
            "unit": '',
            "equipmentStyle": '',
            "equipmentModule": '',
            "startDelay": null,
            "endDelay": null,
            "property": '',
            "propertyArr": [],
            "equipmentBaseType": null,
            "eventExpression": '',
            "equipmentNo": '',
            "assetState": null,
            "buyDate": '',
            "usedDate": '',
            "usedLimit": null,
            "price": null,
            "ratedCapacity": '',
            "installedModule": '',
            "equipmentProjectInfo": {
                "projectName": '',
                "contractNo": ''
            },
            "description": '',
            popVisible: false
        };
    equipmentCategoryArr = [];
    equipmentTypeArr = [];
    vendorArr = [];
    propertyArr = [];
    assetStateArr = [];
    equipmentClassArr = [];
    equipmentStateArr = [];
    parentEquipmentList = [];
    batteryData: any;
    batteryFlag = false;
    submitted: any;

    constructor(injector: Injector,
        private message: NzMessageService,
        private service: DeviceManagementService,
        private dataDictionaryService: DataDictionaryService,
        private baseClassService: BaseClassService,
        private global: GlobalState,
        private modal: NzModalService) {
        super(injector);
    }

    onInit() {
        this.activatedRoute.params.subscribe((params) => {
            if (params && params['id']) this.equipmentId = params['id'];
        });
        //获取数据字典
        const mapping: { [key: string]: number } = {
            equipmentCategory: 7, //类型 equipmentCategoryArr
            equipmentType: 8, //分类 equipmentTypeArr
            vendor: 14, //厂商  vendorArr
            property: 9, //属性  propertyArr
            assetState: 10, //资产状态  assetStateArr
            equipmentClass: 11, //电池类型  equipmentClassArr
            equipmentState: 12, //电池工作状态  equipmentStateArr
        };

        Promise.all([
            ...Object.values(mapping).map(entryId => this.dataDictionaryService.getDataItems(entryId)),
            this.service.getParentDevice(this.equipmentId),
            this.baseClassService.getEquipmentBaseType(),
            this.service.getBatteryDeviceCategory(),
        ]).then(results => {
            const idx = Object.values(mapping).length;
            const dataDictionaryResults = results.slice(0, idx);
            const parentDeviceResult = results[idx];
            const baseTypeResult = results[idx + 1];
            this.batteryData = results[idx + 2];

            Object.keys(mapping).forEach((key, index) => {
                const res = dataDictionaryResults[index];
                if (res.length) {
                    if (index === 2) {
                        this.vendorArr = res.map((item: any) => ({
                            label: item['itemValue'], value: item['itemValue']
                        }));
                    } else {
                        (this as any)[key + 'Arr'] = res.map((item: any) => ({
                            label: item['itemValue'], value: item['itemId']
                        }));
                    }
                }
            });

            if (parentDeviceResult.length) {
                this.parentEquipmentList = parentDeviceResult.map((item: any) => ({
                    label: item['equipmentName'], value: item['equipmentId'].toString()
                }));
            }
            if (baseTypeResult.length) {
                this.baseTypeList = baseTypeResult.map((item: any) => ({
                    label: item['baseEquipmentName'], value: item['baseEquipmentId']
                }));
            }

            this.getDeviceInfo();
        }).catch(error => {
            console.log('获取数据字典失败', error);
        });
    }

    getDeviceInfo() {
        if (!this.equipmentId) return;
        this.service.getDeviceInfo(this.equipmentId).then(res => {
            if (res) {
                this.deviceInfo = res;
                if (res.property) {
                    this.deviceInfo.propertyArr = res.property.split('/').map(Number);
                }
                if (res.equipmentCategory in this.batteryData.data) {
                    this.batteryFlag = true;
                }
            }
        })
    }

    // 表达式 
    openExpression() {
        const model = this.openDialog({
            nzTitle: '',
            nzContent: ExpressionConfigurationComponent,
            nzWidth: 600,
            nzData: {
                templateId: this.deviceInfo['equipmentTemplateId'], expression: this.deviceInfo['eventExpression'], type: 0
            },
            nzFooter: [
                {
                    label: '确定',
                    type: 'primary',
                    disabled: componentInstance => !componentInstance!.correct,
                    onClick: (instance: ExpressionConfigurationComponent): void => {
                        instance.confirm()
                    }
                }
            ]
        })
        model.afterClose.subscribe(res => {
            if (res) {
                this.deviceInfo['eventExpression'] = res.expression;
            }
        })
    }

    save(ngForm: any) {
        if (!ngForm) return;
        if (this.deviceInfo.propertyArr?.length) {
            this.deviceInfo.property = this.deviceInfo.propertyArr.join('/');
        }
        this.deviceInfo.buyDate = DateUtil.format(new Date(this.deviceInfo.buyDate), 'yyyy-MM-dd hh:mm:ss');
        this.deviceInfo.usedDate = DateUtil.format(new Date(this.deviceInfo.usedDate), 'yyyy-MM-dd hh:mm:ss');
        this.service.updateDeviceInfo(this.deviceInfo).then(res => {
            if (res) {
                this.message.success('保存成功！')
            }
        })
    }
}