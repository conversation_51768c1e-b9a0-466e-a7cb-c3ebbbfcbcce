package org.siteweb.config.primary.controller;

import lombok.extern.slf4j.Slf4j;
import org.siteweb.config.common.entity.TblStationCMCC;
import org.siteweb.config.common.utils.ResponseResult;
import org.siteweb.config.primary.service.StationCMCCService;
import org.siteweb.config.toolkit.utils.ResponseHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/stationcmcc")
public class StationCMCCController {
    @Autowired
    StationCMCCService stationCMCCService;

    @GetMapping
    public ResponseEntity<ResponseResult> getAll(){
        stationCMCCService.init();
        return ResponseHelper.successful(stationCMCCService.findAll());
    }

    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody TblStationCMCC tblStationCMCC){
        return ResponseHelper.successful(stationCMCCService.update(tblStationCMCC));
    }
}
