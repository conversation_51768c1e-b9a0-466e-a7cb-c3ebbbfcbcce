package org.siteweb.config.primary.service;

import org.siteweb.config.common.entity.TblSysConfig;
import org.siteweb.config.primary.dto.DeploymentConfigDTO;
import org.siteweb.config.primary.enums.StandardCategoryEnum;
import org.siteweb.config.primary.enums.SysConfigEnum;

public interface SysConfigService {
    /**
     * 获取标准类型
     *
     * @return {@link StandardCategoryEnum} 标准类型枚举
     */
    StandardCategoryEnum findStandardCategoryEnum();

    String getStringValueByKey(SysConfigEnum sysConfigEnum);

    TblSysConfig findByKey(SysConfigEnum sysConfigEnum);

    Boolean createSysConfig(TblSysConfig sysConfig);

    void updateSysConfig(TblSysConfig tblSysConfig);

    /**
     * 更新标准化字典版本信息
     */
    void updateBDicVersion();

    /**
     * 获取部署配置
     * @return {@link DeploymentConfigDTO} 部署配置DTO
     */
    DeploymentConfigDTO getDeploymentConfig();

    /**
     * 更新部署配置
     * @param deploymentConfig 部署配置DTO
     */
    void updateDeploymentConfig(DeploymentConfigDTO deploymentConfig);

    /**
     * 获取字符串值，如果不存在则返回默认值
     * @param sysConfigEnum 配置枚举
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getStringValueByKeyOrDefault(SysConfigEnum sysConfigEnum, String defaultValue);

    /**
     * 获取整数值，如果不存在则返回默认值
     * @param sysConfigEnum 配置枚举
     * @param defaultValue 默认值
     * @return 配置值
     */
    Integer getIntValueByKeyOrDefault(SysConfigEnum sysConfigEnum, Integer defaultValue);

    /**
     * 获取布尔值，如果不存在则返回默认值
     * @param sysConfigEnum 配置枚举
     * @param defaultValue 默认值
     * @return 配置值
     */
    Boolean getBoolValueByKeyOrDefault(SysConfigEnum sysConfigEnum, Boolean defaultValue);
}
