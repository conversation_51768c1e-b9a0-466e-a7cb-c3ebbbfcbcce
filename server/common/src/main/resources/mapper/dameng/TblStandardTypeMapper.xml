<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.TblStandardTypeMapper">

    <select id="judgeSiteStandardTableExist" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ALL_TABLES
        WHERE OWNER = sys_context('USERENV','CURRENT_SCHEMA')
        AND TABLE_NAME = #{tableName}
    </select>
</mapper>