<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.siteweb.config.common.mapper.SyncSiteWeb2Mapper">
    <select id="getIsScValue" resultType="java.lang.Integer">
        SELECT CAST(a.systemconfigValue AS SIGNED Integer)
        FROM systemconfig a
        WHERE systemConfigKey = 'system.isSC'
    </select>

    <update id="updateResourceStructureForSc">
        UPDATE resourcestructure
        SET structureTypeId = 102
        WHERE LENGTH(LevelofPath)-LENGTH(REPLACE(LevelofPath,'.','')) = 0
        AND structureTypeId = 101;

        UPDATE resourcestructure
        SET structureTypeId = 103
        WHERE LENGTH(LevelofPath)-LENGTH(REPLACE(LevelofPath,'.','')) = 1
        AND structureTypeId = 102
    </update>

    <update id="revertResourceStructureForSc">
        UPDATE resourcestructure
        SET structureTypeId = 101
        WHERE LENGTH(LevelofPath)-LENGTH(REPLACE(LevelofPath,'.','')) = 0
        AND structureTypeId = 102;

        UPDATE resourcestructure
        SET structureTypeId = 102
        WHERE LENGTH(LevelofPath)-LENGTH(REPLACE(LevelofPath,'.','')) = 1
        AND structureTypeId = 103
    </update>

    <update id="updateInitialStructureType">
        UPDATE resourcestructure
        SET structureTypeId = 102
        WHERE structureTypeId = 2
    </update>

    <insert id="insertCenterStructure">
        INSERT INTO resourcestructure(
        ResourceStructureId, SceneId, structureTypeId,
        ResourceStructureName, ParentResourceStructureId,
        photo, Position, LevelOfPath, Display,
        SortValue, OriginId, OriginParentId
        )
        SELECT
        (SELECT ItemId * 1000000 + 1 FROM tbl_dataitem WHERE entryId = 62),
        2, 102, StructureName, 0, null, null, null, 1, 1,
        (SELECT ItemId FROM tbl_dataitem WHERE entryId = 62), 0
        FROM TBL_StationStructure a
        WHERE ParentStructureId = 0
        AND NOT EXISTS(
        SELECT 'x'
        FROM resourcestructure b
        WHERE b.OriginId = (SELECT ItemId FROM tbl_dataitem WHERE entryId = 62)
        AND b.structureTypeId = 102
        )
    </insert>

    <update id="updateCenterStructureName">
        UPDATE resourcestructure a
        INNER JOIN TBL_StationStructure b
        ON a.OriginId = b.StructureId
        AND a.structureTypeId = 102
        SET a.ResourceStructureName = b.StructureName
    </update>

    <delete id="deleteCenterStructure">
        DELETE a
        FROM resourcestructure a
        WHERE a.structureTypeId = 102
        AND NOT EXISTS(
        SELECT 1
        FROM TBL_StationStructure b
        WHERE a.OriginId = b.StructureId
        )
    </delete>

    <update id="updateCenterStructurePath">
        UPDATE resourcestructure
        SET
        ParentResourceStructureId = 0,
        LevelOfPath = CONCAT(CAST(ResourceStructureId AS CHAR))
        WHERE structureTypeId = 102
    </update>

    <insert id="insertSubStructure">
        INSERT INTO resourcestructure(
        SceneId, structureTypeId, ResourceStructureName,
        ParentResourceStructureId, photo, Position,
        LevelOfPath, Display, SortValue,
        OriginId, OriginParentId
        )
        SELECT
        2, 103, StructureName, 0, null, null, null, 1, 1,
        StructureId, parentStructureId
        FROM TBL_StationStructure a
        WHERE a.StructureGroupId = 1
        AND NOT EXISTS(
        SELECT 'x'
        FROM resourcestructure b
        WHERE b.OriginId = a.StructureId
        AND b.structureTypeId = 103
        )
    </insert>

    <update id="updateSubStructureName">
        UPDATE resourcestructure a
        INNER JOIN TBL_StationStructure b
        ON a.OriginId = b.StructureId
        AND a.structureTypeId = 103
        SET a.ResourceStructureName = b.StructureName
    </update>

    <delete id="deleteSubStructure">
        DELETE a
        FROM resourcestructure a
        WHERE a.structureTypeId = 103
        AND NOT EXISTS(
        SELECT 1
        FROM TBL_StationStructure b
        WHERE a.OriginId = b.StructureId
        AND b.StructureGroupId = 1
        )
    </delete>

    <update id="updateSubStructureParentPath">
        UPDATE resourcestructure a
        INNER JOIN resourcestructure b
        ON a.OriginParentId = b.OriginId
        SET
        a.ParentResourceStructureId = b.ResourceStructureId,
        a.LevelOfPath = CONCAT(b.LevelOfPath, '.', CAST(a.ResourceStructureId AS CHAR))
        WHERE a.structureTypeId = 103
        AND b.structureTypeId = 102
    </update>

    <update id="updateSubStructureSecondLevel">
        UPDATE resourcestructure a
        INNER JOIN resourcestructure b
        ON a.OriginParentId = b.OriginId
        SET
        a.ParentResourceStructureId = b.ResourceStructureId,
        a.LevelOfPath = CONCAT(b.LevelOfPath, '.', CAST(a.ResourceStructureId AS CHAR))
        WHERE a.structureTypeId = 103
        AND b.structureTypeId = 103
    </update>

    <insert id="insertStationStructure">
        INSERT INTO resourcestructure(
        SceneId, structureTypeId, ResourceStructureName,
        ParentResourceStructureId, photo, Position,
        LevelOfPath, Display, SortValue,
        OriginId, OriginParentId
        )
        SELECT
        2, 104, StationName, rs.ResourceStructureId, null, null,
        rs.LevelOfPath, 1, 1, a.StationId, b.StructureId
        FROM TBL_Station a
        INNER JOIN tbl_stationstructuremap b ON a.StationId = b.StationId
        INNER JOIN TBL_StationStructure c
        ON b.StructureId = c.StructureId
        AND c.StructureGroupId IN(0,1)
        INNER JOIN resourcestructure rs
        ON c.StructureId = rs.OriginId
        AND rs.structureTypeId IN(102,103)
        WHERE NOT EXISTS(
        SELECT 'x'
        FROM resourcestructure b
        WHERE b.OriginId = a.StationId
        AND b.structureTypeId = 104
        )
    </insert>

    <update id="updateStationStructure">
        UPDATE resourcestructure a
        INNER JOIN TBL_Station b
        ON a.OriginId = b.StationId
        AND a.structureTypeId = 104
        INNER JOIN tbl_stationstructuremap c ON c.StationId = b.StationId
        INNER JOIN TBL_StationStructure d
        ON d.StructureId = c.StructureId
        AND d.StructureGroupId IN(0,1)
        INNER JOIN resourcestructure rs
        ON d.StructureId = rs.OriginId
        AND rs.structureTypeId IN(102,103)
        SET
        a.ResourceStructureName = b.StationName,
        a.OriginParentId = d.StructureId,
        a.LevelOfPath = rs.LevelOfPath,
        a.ParentResourceStructureId = rs.ResourceStructureId
    </update>

    <delete id="deleteStationStructure">
        DELETE a
        FROM resourcestructure a
        WHERE a.structureTypeId = 104
        AND NOT EXISTS(
        SELECT 1
        FROM TBL_Station b
        WHERE a.OriginId = b.StationId
        )
    </delete>

    <update id="updateStationStructurePath">
        UPDATE resourcestructure a
        INNER JOIN resourcestructure b
        ON a.OriginParentId = b.OriginId
        SET
        a.ParentResourceStructureId = b.ResourceStructureId,
        a.LevelOfPath = CONCAT(b.LevelOfPath, '.', CAST(a.ResourceStructureId AS CHAR))
        WHERE a.structureTypeId = 104
        AND b.structureTypeId = 103
    </update>

    <insert id="insertHouseStructure">
        INSERT INTO resourcestructure(
        SceneId, structureTypeId, ResourceStructureName,
        ParentResourceStructureId, photo, Position,
        LevelOfPath, Display, SortValue,
        OriginId, OriginParentId
        )
        SELECT
        2, 105, HouseName, rs.ResourceStructureId, null, null,
        rs.LevelOfPath, 1, 1, HouseId, StationId
        FROM TBL_House a
        INNER JOIN resourcestructure rs
        ON a.StationId = rs.OriginId
        AND rs.structureTypeId = 104
        WHERE NOT EXISTS(
        SELECT 'x'
        FROM resourcestructure b
        WHERE b.OriginId = a.HouseId
        AND b.OriginParentId = a.StationId
        AND b.structureTypeId = 105
        )
    </insert>

    <update id="updateHouseStructure">
        UPDATE resourcestructure a
        INNER JOIN TBL_House b
        ON a.OriginId = b.HouseId
        AND a.OriginParentId = b.StationId
        AND a.structureTypeId = 105
        INNER JOIN resourcestructure rs
        ON b.StationId = rs.OriginId
        AND rs.structureTypeId = 104
        SET a.ResourceStructureName = b.HouseName
        WHERE a.ResourceStructureName != b.houseName
    </update>

    <delete id="deleteHouseStructure">
        DELETE a
        FROM resourcestructure a
        WHERE a.structureTypeId = 105
        AND NOT EXISTS(
        SELECT 1
        FROM TBL_House b
        WHERE a.OriginId = b.HouseId
        AND a.OriginParentId = b.StationId
        )
    </delete>

    <update id="updateHouseStructurePath">
        UPDATE resourcestructure a
        INNER JOIN resourcestructure b
        ON a.OriginParentId = b.OriginId
        SET
        a.ParentResourceStructureId = b.ResourceStructureId,
        a.LevelOfPath = CONCAT(b.LevelOfPath, '.', CAST(a.ResourceStructureId AS CHAR))
        WHERE a.structureTypeId = 105
        AND b.structureTypeId = 104
    </update>

    <update id="updateEquipmentResourceStructure">
        UPDATE TBL_Equipment a
        INNER JOIN resourcestructure b
        ON a.StationId = b.OriginParentId
        AND a.HouseId = b.OriginId
        SET a.ResourceStructureId = b.ResourceStructureId
        WHERE a.ResourceStructureId = 0
    </update>

    <insert id="insertConfigChangeLog">
        INSERT INTO tbl_configchangemacrolog
        VALUES ('-1', 27, 1, NOW());

        INSERT INTO tbl_configchangemacrolog
        VALUES ('-1', 3, 1, NOW())
    </insert>


</mapper>