package org.siteweb.config.common.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 事件(告警)基类字典列表
 *
 * <AUTHOR> (2024/4/26)
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldNameConstants
@ExcelIgnoreUnannotated
public class EventBaseDictionaryDTO {
    /**
     * 事件/告警基础类别id
     */
    @ExcelProperty(value = "告警基础类别ID")
    private Long baseTypeId;
    /**
     * 告警基类名
     */
    @ExcelProperty(value = "告警基础类别名称")
    private String baseTypeName;
    /**
     * 基类设备ID
     */
    private Integer baseEquipmentId;
    /**
     * 基础类别名称(大类名称)
     */
    @ExcelProperty(value = "基础设备大类")
    private String baseClassName;
    /**
     * 基础设备子类名称(设备类型)
     */
    @ExcelProperty(value = "基础设备子类")
    private String baseEquipmentName;
    /**
     * 告警基类名（英文）
     */
    private String englishName;
    /**
     * 告警等级ID
     */
    private Integer eventSeverityId;
    /**
     * 告警等级
     */
    private String eventSeverityName;
    /**
     * 告警开始值
     */
    private Double comparedValue;
    /**
     * 告警逻辑分类
     */
    private Integer baseLogicCategoryId;
    /**
     * 告警逻辑分类名称
     */
    @ExcelProperty(value = "告警逻辑分类")
    private String baseLogicCategoryName;
    /**
     * 告警开始延时
     */
    private Integer startDelay;
    /**
     * 告警结束延时
     */
    private Integer endDelay;
    /**
     * 扩展信息1
     */
    private String extendField1;
    /**
     * 扩展信息2
     */
    private String extendField2;
    /**
     * 扩展信息3
     */
    private String extendField3;
    /**
     * 扩展信息4
     */
    private String extendField4;
    /**
     * 扩展信息5
     */
    private String extendField5;
    /**
     * 扩展信息
     */
    @ExcelProperty(value = "名称扩展表达式")
    private String baseNameExt;
    /**
     * 是否为系统
     */
    @ExcelProperty(value = "是否系统内置")
    private Boolean isSystem;
    /**
     * 描述信息
     */
    @ExcelProperty(value = "备注")
    private String description;

}
