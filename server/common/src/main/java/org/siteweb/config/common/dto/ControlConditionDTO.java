package org.siteweb.config.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Creation Date: 2024/5/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ControlConditionDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 控制id
     */
    private Integer controlId;
    /**
     * 控制名
     */
    private String controlName;
    /**
     * tbl_controlmeanings value
     */
    private Integer parameterValue;

    /**
     * 基类id
     */
    private Long baseTypeId;

    /**
     * 基类含义id
     */
    private Integer baseCondId;
}
