package org.siteweb.config.common.change;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.utils.JsonHelper;

import java.time.LocalDateTime;


/**
 * <AUTHOR> (2024-02-22)
 **/
@Data
@NoArgsConstructor
public class ChangeMessage {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;


    private Object body;


    public ChangeMessage(Object data) {
        this.body = data;
        this.changeTime = LocalDateTime.now();
    }


    /**
     * 读取变更消息的Body数据
     *
     * @param tyleClass 对象类型的class 如 TBL_Equipment.class
     * <AUTHOR> (2024/3/9)
     */
    public <T> T readBody(Class<T> tyleClass) {
        return JsonHelper.convertTo(body, tyleClass);
    }


}
