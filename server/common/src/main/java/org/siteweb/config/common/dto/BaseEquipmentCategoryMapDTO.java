package org.siteweb.config.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.siteweb.config.common.entity.TblBaseEquipmentCategoryMap;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/8/28
 */
@Data
@NoArgsConstructor
public class BaseEquipmentCategoryMapDTO {

    private List<TblBaseEquipmentCategoryMap> baseEquipmentCategoryMapList;

    private String standardName;
}
